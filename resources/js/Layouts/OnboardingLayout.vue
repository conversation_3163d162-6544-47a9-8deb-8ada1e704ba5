<template>
    <main class="flex h-screen w-full">
        <!-- Left Side ~856px on 1440px -->
        <div class="flex flex-col justify-center items-center w-3/5">
            <img src="/images/logo.png" height="68px" width="60px" alt="" />
            <Stepper v-if="currentStep" :steps="[1, 1, 1, 1, 1, 1]" :currentStep="currentStep" class="mb-48" />
            <slot />
        </div>

        <!-- Right Side ~584px on 1440px -->
        <div class="relative hidden bg-surface-information-light lg:flex w-2/5 items-end justify-start">
            <slot name="image"></slot>
        </div>
    </main>
    <CookieConsent />
</template>

<script setup>
import Stepper from "@/Components/Stepper/Stepper.vue";
import CookieConsent from "@/Components/CookieConsent/CookieConsent.vue";

defineProps({
    title: String,
    currentStep: Object,
});
</script>
