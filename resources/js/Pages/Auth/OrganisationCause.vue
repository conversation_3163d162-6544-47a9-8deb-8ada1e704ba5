<template>
    <Head title="Organisation cause">
        <meta
            name="description"
            content="Tell us about the cause your organisation helps."
        />
    </Head>

    <OnboardingLayout :currentStep="{ step: 3, sub: 1 }">
        <div>
            <p class="mb-48">To help us generate content for you</p>

            <h1 class="fs-2xl font-bold">
                Tell us about the cause your organisation helps<span
                    class="text-text-action"
                    >*</span
                >
            </h1>

            <form @submit.prevent="submit" class="mt-40">
                <SelectInput
                    label="General category"
                    labelPosition="outside"
                    class="w-full"
                    :options="findDonorsStore.generalCategoryOptions"
                    v-model="form.general_category"
                    @change="fetchSubCategories"
                    :disabled="
                        findDonorsStore.generalCategoryOptions.length < 1
                    "
                    :serverError="form.errors.general_category"
                />

                <SelectInput
                    label="Sub-category"
                    labelPosition="outside"
                    class="w-full"
                    :options="findDonorsStore.subCategoryOptions"
                    v-model="form.sub_category"
                    :disabled="findDonorsStore.subCategoryOptions.length < 1"
                    :serverError="form.errors.sub_category"
                />

                <div class="mt-24 flex justify-between">
                    <button
                        @click.prevent="back"
                        class="btn--secondary"
                        :disabled="form.processing"
                    >
                        <IconArrowLeft />
                        Back
                    </button>

                    <button
                        type="submit"
                        class="btn--secondary group"
                        :disabled="form.processing || !form.general_category"
                    >
                        Continue

                        <IconArrowRight
                            class="stroke-text-action-hover group-disabled:stroke-text-disabled"
                        />
                    </button>
                </div>
            </form>
        </div>

        <template v-slot:image>
            <img
                class="h-auto w-auto max-w-[600px] lg:max-h-full xl:max-w-[800px] xl:-translate-x-64"
                src="/images/onboarding/onboarding-2.png"
                alt=""
                width="800"
                height="625"
            />
        </template>
    </OnboardingLayout>
</template>

<script setup>
import { Head, useForm, router } from "@inertiajs/vue3";
import OnboardingLayout from "@/Layouts/OnboardingLayout.vue";
import IconArrowRight from "@/Components/Icons/IconArrowRight.vue";
import { ref, onMounted, watch } from "vue";
import IconArrowLeft from "@/Components/Icons/IconArrowLeft.vue";
import SelectInput from "@/Components/SelectInput/SelectInput.vue";
import { fetchGeneralCategories, fetchSubCategories } from "@/utilities/api";

import { useFindDonorsStore } from "@/stores/findDonors";
const findDonorsStore = useFindDonorsStore();
const { setSubCategoryOptions } = findDonorsStore;

const props = defineProps({
    general_category: String,
    sub_category: String,
});

const form = useForm({
    general_category: props.general_category ?? null,
    sub_category: props.sub_category ?? null,
});

// Fetch the general categories on component mount if they are not already available.
onMounted(() => {
    if (!findDonorsStore.generalCategoryOptions.length) {
        fetchGeneralCategories();
    }
    if (props.general_category) {
        fetchSubCategories(props.general_category);
    }
});

// sub-categories reset and fetch what general-categories is selected
watch(
    () => form.general_category,
    async () => {
        // Reset sub-category values immediately
        setSubCategoryOptions([]);
        form.sub_category = null;
        // Always fetch sub-categories when GENERAL_CATEGORY changes
        await fetchSubCategories(form.general_category);
    },
    true,
);

const submit = () => {
    form.post(route("api.setup.about"), {
        onSuccess: () => {
            router.get("/setup/organisation-region");
        },
        onError: (err) => {
            console.log(err);
        },
    });
};

const back = () => {
    router.get("/setup/organisation-mission");
};
</script>
